import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

import '../../domain/entities/deck.dart';
import '../../domain/entities/study_mode.dart';
import '../../presentation/views/create_flashcard_view.dart';
import '../../presentation/views/create_deck_view.dart';
import '../../presentation/views/deck_detail_view.dart';
import '../../presentation/views/main_navigation_view.dart';
import '../../presentation/views/auth/welcome_view.dart';
import '../../presentation/views/auth/new_login_view.dart';
import '../../presentation/views/auth/signup_view.dart';
import '../../presentation/views/auth/forgot_password_view.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  BuildContext? get context => navigatorKey.currentContext;

  // Navigation methods
  Future<void> navigateToStudySession(Deck deck, StudyMode studyMode) async {
    if (context == null) return;
    
    // For now, show a message since StudySessionView needs to be implemented
    showSnackBar('Study session: ${studyMode.displayName} for "${deck.name}"');
  }

  Future<void> navigateToCreateFlashcard(Deck deck) async {
    if (context == null) return;
    
    await Navigator.push(
      context!,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: CreateFlashcardView(deckId: deck.id),
      ),
    );
  }

  Future<bool?> navigateToCreateDeck() async {
    if (context == null) return null;
    
    final result = await Navigator.push<bool>(
      context!,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CreateDeckView(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.1),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              )),
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
    
    return result;
  }

  Future<void> navigateToDeckDetail(Deck deck) async {
    if (context == null) return;
    
    await Navigator.push(
      context!,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: DeckDetailView(deck: deck),
      ),
    );
  }

  void goBack() {
    if (context == null) return;
    Navigator.pop(context!);
  }

  void goBackWithResult<T>(T result) {
    if (context == null) return;
    Navigator.pop(context!, result);
  }

  Future<void> navigateToMainNavigation() async {
    if (context == null) return;
    
    await Navigator.pushReplacement(
      context!,
      MaterialPageRoute(builder: (_) => const MainNavigationView()),
    );
  }

  // Authentication navigation methods
  Future<void> navigateToWelcome() async {
    if (context == null) return;
    
    await Navigator.pushReplacement(
      context!,
      PageTransition(
        type: PageTransitionType.fade,
        child: const WelcomeView(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  Future<void> navigateToLogin() async {
    if (context == null) return;
    
    await Navigator.push(
      context!,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: const NewLoginView(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  Future<void> navigateToSignup() async {
    if (context == null) return;
    
    await Navigator.push(
      context!,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: const SignupView(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  Future<void> navigateToForgotPassword() async {
    if (context == null) return;
    
    await Navigator.push(
      context!,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: const ForgotPasswordView(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  void navigateToLoginReplacement() {
    if (context == null) return;
    
    Navigator.pushReplacement(
      context!,
      PageTransition(
        type: PageTransitionType.fade,
        child: const NewLoginView(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  // Dialog methods
  Future<bool> showConfirmationDialog({
    required String title,
    required String content,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    if (context == null) return false;
    
    final result = await showDialog<bool>(
      context: context!,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  void showSnackBar(String message, {bool isError = false}) {
    if (context == null) return;
    
    ScaffoldMessenger.of(context!).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
      ),
    );
  }

  void showErrorSnackBar(String message) {
    showSnackBar(message, isError: true);
  }
} 