import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'dart:developer' as developer;

import 'core/di/injection_container.dart';
import 'core/services/navigation_service.dart';
import 'core/theme/app_theme.dart';
import 'presentation/viewmodels/theme_view_model.dart';
import 'presentation/viewmodels/user_view_model.dart';
import 'presentation/viewmodels/deck_view_model.dart';
import 'presentation/views/main_navigation_view.dart';
import 'presentation/views/auth/login_view.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize MVVM dependencies
    await initializeDependencies();

    // Initialize ViewModels
    final themeViewModel = getIt<ThemeViewModel>();
    final userViewModel = getIt<UserViewModel>();
    final deckViewModel = getIt<DeckViewModel>();
    
    await themeViewModel.loadTheme();
    await userViewModel.loadUserState();
    await deckViewModel.loadDecks();

    runApp(FlashCardsApp(
      themeViewModel: themeViewModel,
      userViewModel: userViewModel,
      deckViewModel: deckViewModel,
    ));
  } catch (e, stackTrace) {
    developer.log(
      'Error during app initialization',
      name: 'main',
      error: e,
      stackTrace: stackTrace,
    );
    
    // Run a fallback app
    runApp(ErrorApp(error: e.toString()));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error App',
      home: Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const Text(
                'App Initialization Error:',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                error,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FlashCardsApp extends StatelessWidget {
  final ThemeViewModel themeViewModel;
  final UserViewModel userViewModel;
  final DeckViewModel deckViewModel;

  const FlashCardsApp({
    super.key,
    required this.themeViewModel,
    required this.userViewModel,
    required this.deckViewModel,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // MVVM ViewModels
        ChangeNotifierProvider.value(value: themeViewModel),
        ChangeNotifierProvider.value(value: userViewModel),
        ChangeNotifierProvider.value(value: deckViewModel),
      ],
      child: Consumer3<ThemeViewModel, UserViewModel, DeckViewModel>(
        builder: (context, themeViewModel, userViewModel, deckViewModel, _) {
          return DynamicColorBuilder(
            builder: (lightDynamic, darkDynamic) {
              return MaterialApp(
                title: 'FlashCards Pro',
                debugShowCheckedModeBanner: false,
                navigatorKey: getIt<NavigationService>().navigatorKey,
                
                // Professional theme with system dynamic colors
                theme: _buildTheme(lightDynamic, false),
                darkTheme: _buildTheme(darkDynamic, true),
                themeMode: themeViewModel.themeMode,
                
                home: userViewModel.isAuthenticated 
                    ? const MainNavigationView()
                    : const LoginView(),
                
                // Smooth page transitions
                builder: (context, child) {
                  return AnimatedTheme(
                    duration: const Duration(milliseconds: 200),
                    data: Theme.of(context),
                    child: child!,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  ThemeData _buildTheme(ColorScheme? dynamicScheme, bool isDark) {
    final baseTheme = isDark ? AppTheme.dark : AppTheme.light;
    
    if (dynamicScheme != null) {
      return ThemeData.from(
        colorScheme: dynamicScheme,
        useMaterial3: true,
      ).copyWith(
        textTheme: isDark ? AppTheme.darkTextTheme : AppTheme.lightTextTheme,
        appBarTheme: isDark ? AppTheme.darkAppBarTheme : AppTheme.lightAppBarTheme,
        cardTheme: isDark ? AppTheme.darkCardTheme : AppTheme.lightCardTheme,
        elevatedButtonTheme: isDark ? AppTheme.darkElevatedButtonTheme : AppTheme.lightElevatedButtonTheme,
        inputDecorationTheme: isDark ? AppTheme.darkInputDecorationTheme : AppTheme.lightInputDecorationTheme,
        bottomNavigationBarTheme: isDark ? AppTheme.darkBottomNavigationBarTheme : AppTheme.lightBottomNavigationBarTheme,
      );
    }
    
    return baseTheme;
  }
}
