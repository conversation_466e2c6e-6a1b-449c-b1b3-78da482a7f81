import 'package:flutter/material.dart';
import 'package:glass_kit/glass_kit.dart';
import 'package:animate_do/animate_do.dart';
import '../../core/theme/app_theme.dart';

class GradientCard extends StatelessWidget {
  final Widget child;
  final List<Color>? gradient;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? height;
  final double? width;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final bool glassEffect;
  final double elevation;
  final int animationDelay;

  const GradientCard({
    super.key,
    required this.child,
    this.gradient,
    this.padding,
    this.margin,
    this.height,
    this.width,
    this.onTap,
    this.borderRadius,
    this.glassEffect = false,
    this.elevation = 8,
    this.animationDelay = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final borderRadius = this.borderRadius ?? BorderRadius.circular(20);
    
    Widget cardWidget = glassEffect 
        ? _buildGlassCard(context, borderRadius)
        : _buildGradientCard(context, borderRadius, theme);

    cardWidget = FadeInUp(
      delay: Duration(milliseconds: animationDelay),
      duration: const Duration(milliseconds: 600),
      child: cardWidget,
    );

    if (margin != null) {
      cardWidget = Padding(
        padding: margin!,
        child: cardWidget,
      );
    }

    return cardWidget;
  }

  Widget _buildGlassCard(BuildContext context, BorderRadius borderRadius) {
    return GlassContainer(
      height: height ?? double.infinity,
      width: width ?? double.infinity,
      gradient: LinearGradient(
        colors: gradient ?? AppTheme.primaryGradient,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderGradient: LinearGradient(
        colors: [
          Colors.white.withValues(alpha: 0.3),
          Colors.white.withValues(alpha: 0.1),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      blur: 15,
      borderWidth: 1,
      borderRadius: borderRadius,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }

  Widget _buildGradientCard(BuildContext context, BorderRadius borderRadius, ThemeData theme) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradient ?? [
            theme.colorScheme.primary,
            theme.colorScheme.secondary,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: borderRadius,
        boxShadow: [
          BoxShadow(
            color: (gradient?.first ?? theme.colorScheme.primary).withValues(alpha: 0.3),
            blurRadius: elevation,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}

class NeumorphismCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? height;
  final double? width;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final bool isPressed;
  final int animationDelay;

  const NeumorphismCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.height,
    this.width,
    this.onTap,
    this.borderRadius,
    this.isPressed = false,
    this.animationDelay = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final borderRadius = this.borderRadius ?? BorderRadius.circular(20);
    
    Widget cardWidget = AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: borderRadius,
        boxShadow: isPressed 
            ? [
                BoxShadow(
                  color: isDark 
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 5,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: isDark 
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.white,
                  blurRadius: 5,
                  offset: const Offset(-2, -2),
                ),
              ]
            : [
                BoxShadow(
                  color: isDark 
                      ? Colors.black.withValues(alpha: 0.5)
                      : Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(8, 8),
                ),
                BoxShadow(
                  color: isDark 
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.white,
                  blurRadius: 15,
                  offset: const Offset(-8, -8),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );

    cardWidget = FadeInUp(
      delay: Duration(milliseconds: animationDelay),
      duration: const Duration(milliseconds: 600),
      child: cardWidget,
    );

    if (margin != null) {
      cardWidget = Padding(
        padding: margin!,
        child: cardWidget,
      );
    }

    return cardWidget;
  }
} 