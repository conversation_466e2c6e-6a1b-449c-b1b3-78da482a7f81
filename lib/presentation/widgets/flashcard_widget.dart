import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flip_card/flip_card.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:glass_kit/glass_kit.dart';
import '../../domain/entities/flashcard.dart';
import '../../core/theme/app_theme.dart';

class FlashCardWidget extends StatefulWidget {
  final Flashcard flashCard;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final VoidCallback? onShuffle;
  final bool showNavigationButtons;
  final int? currentIndex;
  final int? totalCards;
  final Function(bool isCorrect)? onAnswer;

  const FlashCardWidget({
    super.key,
    required this.flashCard,
    this.onNext,
    this.onPrevious,
    this.onShuffle,
    this.showNavigationButtons = true,
    this.currentIndex,
    this.totalCards,
    this.onAnswer,
  });

  @override
  State<FlashCardWidget> createState() => _FlashCardWidgetState();
}

class _FlashCardWidgetState extends State<FlashCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  bool _isFlipped = false;
  GlobalKey<FlipCardState> cardKey = GlobalKey<FlipCardState>();

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _slideController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _flipCard() {
    setState(() => _isFlipped = !_isFlipped);
    cardKey.currentState?.toggleCard();
    HapticFeedback.lightImpact();
  }

  void _handleSwipe(DismissDirection direction) {
    HapticFeedback.mediumImpact();
    
    if (direction == DismissDirection.endToStart && widget.onNext != null) {
      widget.onNext!();
    } else if (direction == DismissDirection.startToEnd && widget.onPrevious != null) {
      widget.onPrevious!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          height: size.height * 0.7,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              if (widget.showNavigationButtons) _buildHeader(theme),
              const SizedBox(height: 16),
              Expanded(child: _buildCard(theme, size)),
              const SizedBox(height: 16),
              if (widget.showNavigationButtons) _buildFooter(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildHeaderButton(
            icon: PhosphorIcons.shuffle(),
            onTap: widget.onShuffle,
            theme: theme,
          ),
          if (widget.currentIndex != null && widget.totalCards != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                '${widget.currentIndex! + 1} / ${widget.totalCards}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          _buildHeaderButton(
            icon: _isFlipped ? PhosphorIcons.eye() : PhosphorIcons.eyeSlash(),
            onTap: _flipCard,
            theme: theme,
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback? onTap,
    required ThemeData theme,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildCard(ThemeData theme, Size size) {
    return Dismissible(
      key: ValueKey(widget.flashCard.id),
      direction: DismissDirection.horizontal,
      onDismissed: _handleSwipe,
      resizeDuration: null,
      child: FlipCard(
        key: cardKey,
        flipOnTouch: true,
        direction: FlipDirection.HORIZONTAL,
        speed: 600,
        onFlip: () {
          setState(() => _isFlipped = !_isFlipped);
          HapticFeedback.lightImpact();
        },
        front: _buildCardSide(
          content: widget.flashCard.front,
          label: 'Question',
          gradient: AppTheme.primaryGradient,
          theme: theme,
          size: size,
        ),
        back: _buildCardSide(
          content: widget.flashCard.back,
          label: 'Answer',
          gradient: AppTheme.secondaryGradient,
          theme: theme,
          size: size,
        ),
      ),
    );
  }

  Widget _buildCardSide({
    required String content,
    required String label,
    required List<Color> gradient,
    required ThemeData theme,
    required Size size,
  }) {
    return GlassContainer(
        height: double.infinity,
        width: double.infinity,
        gradient: LinearGradient(
          colors: gradient.map((c) => c.withValues(alpha: 0.8)).toList(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderGradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.3),
            Colors.white.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        blur: 20,
        borderWidth: 1,
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Background pattern
            Positioned.fill(
              child: CustomPaint(
                painter: PatternPainter(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Label
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      label,
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Content
                  Expanded(
                    child: Center(
                      child: SingleChildScrollView(
                        child: Text(
                          content,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  
                  // Tap hint
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        PhosphorIcons.hand(),
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tap to flip',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(ThemeData theme) {
    if (widget.onAnswer == null) return const SizedBox.shrink();
    
    return Row(
        children: [
          Expanded(
            child: _buildAnswerButton(
              label: 'Hard',
              icon: PhosphorIcons.xCircle(),
              color: theme.colorScheme.error,
              onTap: () => widget.onAnswer!(false),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildAnswerButton(
              label: 'Easy',
              icon: PhosphorIcons.checkCircle(),
              color: AppTheme.successGradient.first,
              onTap: () => widget.onAnswer!(true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        onTap();
      },
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PatternPainter extends CustomPainter {
  final Color color;

  PatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const spacing = 40.0;
    
    // Draw diagonal lines
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 