import 'package:flutter/material.dart';

class AuthButtonWidget extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;

  const AuthButtonWidget({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height = 56,
    this.padding,
  });

  @override
  State<AuthButtonWidget> createState() => _AuthButtonWidgetState();
}

class _AuthButtonWidgetState extends State<AuthButtonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.onPressed != null && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) => Transform.scale(
        scale: _scaleAnimation.value,
        child: Opacity(
          opacity: _opacityAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              width: widget.width ?? double.infinity,
              height: widget.height,
              padding: widget.padding,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.isOutlined
                        ? Colors.transparent
                        : (widget.backgroundColor ?? 
                           (isEnabled 
                             ? colorScheme.primary 
                             : colorScheme.onSurface.withValues(alpha: 0.12))),
                    border: widget.isOutlined
                        ? Border.all(
                            color: isEnabled 
                              ? colorScheme.primary 
                              : colorScheme.onSurface.withValues(alpha: 0.12),
                            width: 2,
                          )
                        : null,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: !widget.isOutlined && isEnabled
                        ? [
                            BoxShadow(
                              color: (widget.backgroundColor ?? colorScheme.primary)
                                  .withValues(alpha: 0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ]
                        : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: isEnabled ? widget.onPressed : null,
                      borderRadius: BorderRadius.circular(16),
                      splashColor: widget.isOutlined
                          ? colorScheme.primary.withValues(alpha: 0.1)
                          : colorScheme.onPrimary.withValues(alpha: 0.1),
                      highlightColor: widget.isOutlined
                          ? colorScheme.primary.withValues(alpha: 0.05)
                          : colorScheme.onPrimary.withValues(alpha: 0.05),
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        alignment: Alignment.center,
                        child: widget.isLoading
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    widget.isOutlined
                                        ? colorScheme.primary
                                        : colorScheme.onPrimary,
                                  ),
                                ),
                              )
                            : Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (widget.icon != null) ...[
                                    Icon(
                                      widget.icon,
                                      size: 20,
                                      color: widget.textColor ??
                                          (widget.isOutlined
                                              ? (isEnabled 
                                                  ? colorScheme.primary 
                                                  : colorScheme.onSurface.withValues(alpha: 0.38))
                                              : (isEnabled 
                                                  ? colorScheme.onPrimary 
                                                  : colorScheme.onSurface.withValues(alpha: 0.38))),
                                    ),
                                    const SizedBox(width: 8),
                                  ],
                                  Text(
                                    widget.text,
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: widget.textColor ??
                                          (widget.isOutlined
                                              ? (isEnabled 
                                                  ? colorScheme.primary 
                                                  : colorScheme.onSurface.withValues(alpha: 0.38))
                                              : (isEnabled 
                                                  ? colorScheme.onPrimary 
                                                  : colorScheme.onSurface.withValues(alpha: 0.38))),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 