import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animate_do/animate_do.dart';
import '../../core/theme/app_theme.dart';

enum ButtonVariant { primary, secondary, outline, ghost, destructive }

class AnimatedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ButtonVariant variant;
  final bool isLoading;
  final bool isExpanded;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool hapticFeedback;

  const AnimatedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.variant = ButtonVariant.primary,
    this.isLoading = false,
    this.isExpanded = false,
    this.padding,
    this.width,
    this.height,
    this.hapticFeedback = true,
  });

  @override
  State<AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<AnimatedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
    if (widget.hapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget buttonContent = _buildButtonContent(theme);
    
    buttonContent = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: buttonContent,
    );



    return GestureDetector(
      onTapDown: widget.onPressed != null ? _handleTapDown : null,
      onTapUp: widget.onPressed != null ? _handleTapUp : null,
      onTapCancel: widget.onPressed != null ? _handleTapCancel : null,
      onTap: widget.onPressed != null && !widget.isLoading
          ? () {
              widget.onPressed!();
              if (widget.hapticFeedback) {
                HapticFeedback.selectionClick();
              }
            }
          : null,
      child: buttonContent,
    );
  }

  Widget _buildButtonContent(ThemeData theme) {
    final colors = _getButtonColors(theme);
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: widget.isExpanded ? double.infinity : widget.width,
      height: widget.height ?? 56,
      padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 24),
      decoration: _getButtonDecoration(colors, isEnabled),
      child: Center(
        child: widget.isLoading 
            ? _buildLoadingContent(colors)
            : _buildNormalContent(colors),
      ),
    );
  }

  Widget _buildLoadingContent(ButtonColors colors) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(colors.foreground),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Loading...',
          style: TextStyle(
            color: colors.foreground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildNormalContent(ButtonColors colors) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: colors.foreground,
            size: 20,
          ),
          const SizedBox(width: 8),
        ],
        Text(
          widget.text,
          style: TextStyle(
            color: colors.foreground,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  BoxDecoration _getButtonDecoration(ButtonColors colors, bool isEnabled) {
    switch (widget.variant) {
      case ButtonVariant.primary:
        return BoxDecoration(
          gradient: isEnabled
              ? LinearGradient(
                  colors: [colors.background, colors.backgroundSecondary],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isEnabled ? null : colors.background.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(16),
          boxShadow: isEnabled && !_isPressed
              ? [
                  BoxShadow(
                    color: colors.background.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        );
      
      case ButtonVariant.secondary:
        return BoxDecoration(
          color: colors.background,
          borderRadius: BorderRadius.circular(16),
          boxShadow: isEnabled && !_isPressed
              ? [
                  BoxShadow(
                    color: colors.background.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        );
      
      case ButtonVariant.outline:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isEnabled 
                ? colors.background 
                : colors.background.withValues(alpha: 0.5),
            width: 2,
          ),
        );
      
      case ButtonVariant.ghost:
        return BoxDecoration(
          color: _isPressed 
              ? colors.background.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        );
      
      case ButtonVariant.destructive:
        return BoxDecoration(
          gradient: isEnabled
              ? LinearGradient(
                  colors: AppTheme.warningGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isEnabled ? null : AppTheme.warningGradient.first.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(16),
          boxShadow: isEnabled && !_isPressed
              ? [
                  BoxShadow(
                    color: AppTheme.warningGradient.first.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        );
    }
  }

  ButtonColors _getButtonColors(ThemeData theme) {
    switch (widget.variant) {
      case ButtonVariant.primary:
        return ButtonColors(
          background: theme.colorScheme.primary,
          backgroundSecondary: theme.colorScheme.secondary,
          foreground: theme.colorScheme.onPrimary,
        );
      
      case ButtonVariant.secondary:
        return ButtonColors(
          background: theme.colorScheme.secondaryContainer,
          backgroundSecondary: theme.colorScheme.secondaryContainer,
          foreground: theme.colorScheme.onSecondaryContainer,
        );
      
      case ButtonVariant.outline:
        return ButtonColors(
          background: theme.colorScheme.primary,
          backgroundSecondary: theme.colorScheme.primary,
          foreground: theme.colorScheme.primary,
        );
      
      case ButtonVariant.ghost:
        return ButtonColors(
          background: theme.colorScheme.primary,
          backgroundSecondary: theme.colorScheme.primary,
          foreground: theme.colorScheme.primary,
        );
      
      case ButtonVariant.destructive:
        return ButtonColors(
          background: theme.colorScheme.error,
          backgroundSecondary: AppTheme.warningGradient.last,
          foreground: theme.colorScheme.onError,
        );
    }
  }
}

class ButtonColors {
  final Color background;
  final Color backgroundSecondary;
  final Color foreground;

  ButtonColors({
    required this.background,
    required this.backgroundSecondary,
    required this.foreground,
  });
}

class FloatingActionButtonAnimated extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final String? tooltip;
  final bool mini;
  final List<Color>? gradient;

  const FloatingActionButtonAnimated({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.mini = false,
    this.gradient,
  });

  @override
  State<FloatingActionButtonAnimated> createState() => 
      _FloatingActionButtonAnimatedState();
}

class _FloatingActionButtonAnimatedState extends State<FloatingActionButtonAnimated>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTapDown: (_) {
        _controller.forward();
        HapticFeedback.lightImpact();
      },
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: () {
        widget.onPressed();
        HapticFeedback.selectionClick();
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Container(
                width: widget.mini ? 40 : 56,
                height: widget.mini ? 40 : 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: widget.gradient ?? AppTheme.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(widget.mini ? 20 : 28),
                  boxShadow: [
                    BoxShadow(
                      color: (widget.gradient?.first ?? theme.colorScheme.primary)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  widget.icon,
                  color: Colors.white,
                  size: widget.mini ? 20 : 24,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
} 