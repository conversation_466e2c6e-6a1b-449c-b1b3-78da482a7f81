import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:confetti/confetti.dart';

import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../widgets/flashcard_widget.dart';

class PracticeSessionView extends StatefulWidget {
  final Deck deck;
  final List<Flashcard> cards;

  const PracticeSessionView({
    super.key,
    required this.deck,
    required this.cards,
  });

  @override
  State<PracticeSessionView> createState() => _PracticeSessionViewState();
}

class _PracticeSessionViewState extends State<PracticeSessionView>
    with TickerProviderStateMixin {
  
  late AnimationController _progressController;
  late AnimationController _cardController;
  late ConfettiController _confettiController;
  
  int _currentIndex = 0;
  List<Flashcard> _sessionCards = [];
  final Map<String, int> _cardScores = {};
  int _correctAnswers = 0;
  int _totalAnswered = 0;
  bool _sessionComplete = false;
  

  @override
  void initState() {
    super.initState();
    _initializeSession();
    _initializeAnimations();
  }

  void _initializeSession() {
    _sessionCards = List.from(widget.cards)..shuffle();
    
    // Initialize scores for spaced repetition
    for (final card in _sessionCards) {
      _cardScores[card.id] = 0;
    }
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );
    
    _progressController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _cardController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    if (_sessionComplete) {
      return _buildCompletionScreen(theme, size);
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(theme),
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: SafeArea(
          child: Column(
            children: [
              _buildProgressSection(theme),
              Expanded(
                child: _buildCardSection(theme, size),
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          PhosphorIcons.x(),
          color: theme.colorScheme.onSurface,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: FadeIn(
        duration: const Duration(milliseconds: 600),
        child: Text(
          'Practice Session',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      centerTitle: true,
    );
  }

  BoxDecoration _buildBackgroundDecoration(ThemeData theme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: theme.brightness == Brightness.dark
            ? [
                const Color(0xFF0F172A),
                const Color(0xFF1E293B),
              ]
            : [
                const Color(0xFFFAFAFA),
                const Color(0xFFF1F5F9),
              ],
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme) {
    final progress = _sessionCards.isEmpty ? 0.0 : (_currentIndex / _sessionCards.length);
    // final accuracy = _totalAnswered == 0 ? 0.0 : (_correctAnswers / _totalAnswered);

    return FadeInDown(
      duration: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text(
              '${_currentIndex + 1} / ${_sessionCards.length}',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              color: theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardSection(ThemeData theme, Size size) {
    if (_sessionCards.isEmpty) {
      return Center(
        child: Text(
          'No cards to practice',
          style: theme.textTheme.headlineSmall,
        ),
      );
    }

    return FlashCardWidget(
      flashCard: _sessionCards[_currentIndex],
      currentIndex: _currentIndex,
      totalCards: _sessionCards.length,
      onAnswer: _handleAnswer,
      onNext: _nextCard,
      onPrevious: _previousCard,
    );
  }

  Widget _buildCompletionScreen(ThemeData theme, Size size) {
    final accuracy = _totalAnswered == 0 ? 0.0 : (_correctAnswers / _totalAnswered);

    return Scaffold(
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  PhosphorIcons.trophy(),
                  size: 80,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 24),
                Text(
                  'Session Complete!',
                  style: theme.textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Accuracy: ${(accuracy * 100).toInt()}%',
                  style: theme.textTheme.headlineSmall,
                ),
                Text(
                  'Correct: $_correctAnswers/${_sessionCards.length}',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Back to Deck'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleAnswer(bool isCorrect) {
    setState(() {
      _totalAnswered++;
      if (isCorrect) {
        _correctAnswers++;
      }
    });

    // Auto advance after a delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) _nextCard();
    });
  }

  void _nextCard() {
    if (_currentIndex < _sessionCards.length - 1) {
      setState(() {
        _currentIndex++;
      });
    } else {
      setState(() {
        _sessionComplete = true;
      });
    }
  }

  void _previousCard() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
    }
  }
} 