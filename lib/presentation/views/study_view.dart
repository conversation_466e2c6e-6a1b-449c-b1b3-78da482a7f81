import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';

import '../../domain/entities/deck.dart';
import '../../domain/entities/study_mode.dart';
import '../../domain/usecases/study/update_deck_last_studied.dart';
import '../viewmodels/study_view_model.dart';
import '../widgets/gradient_card_widget.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/navigation_service.dart';
import '../../core/di/injection_container.dart';

class StudyView extends StatefulWidget {
  final Deck deck;

  const StudyView({super.key, required this.deck});

  @override
  State<StudyView> createState() => _StudyViewState();
}

class _StudyViewState extends State<StudyView> 
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ChangeNotifierProvider(
      create: (_) => StudyViewModel(
        updateDeckLastStudied: getIt<UpdateDeckLastStudied>(),
        navigationService: getIt<NavigationService>(),
      )..setDeck(widget.deck),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: _buildAppBar(theme),
        body: Container(
          decoration: _buildBackgroundDecoration(theme),
          child: SafeArea(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(24),
              child: Consumer<StudyViewModel>(
                builder: (context, viewModel, _) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      _buildHeader(theme),
                      const SizedBox(height: 32),
                      _buildDeckInfo(theme, viewModel),
                      const SizedBox(height: 32),
                      _buildStudyModes(theme, viewModel),
                      const SizedBox(height: 32),
                      _buildQuickStats(theme, viewModel),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          PhosphorIcons.arrowLeft(),
          color: theme.colorScheme.onSurface,
        ),
        onPressed: () => context.read<StudyViewModel>().navigateBack(),
      ),
      title: FadeIn(
        duration: const Duration(milliseconds: 600),
        child: Text(
          'Study Mode',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ),
      centerTitle: true,
    );
  }

  BoxDecoration _buildBackgroundDecoration(ThemeData theme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: theme.brightness == Brightness.dark
            ? [
                const Color(0xFF0F172A),
                const Color(0xFF1E293B),
              ]
            : [
                const Color(0xFFFAFAFA),
                const Color(0xFFF1F5F9),
              ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return FadeInDown(
      duration: const Duration(milliseconds: 800),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ready to study?',
            style: theme.textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose your preferred study mode below',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeckInfo(ThemeData theme, StudyViewModel viewModel) {
    return FadeInUp(
      delay: const Duration(milliseconds: 200),
      duration: const Duration(milliseconds: 800),
      child: GradientCard(
        gradient: AppTheme.primaryGradient,
        animationDelay: 400,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    PhosphorIcons.stack(),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.deck.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${viewModel.totalCards} flashcards',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (widget.deck.description.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                widget.deck.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStudyModes(ThemeData theme, StudyViewModel viewModel) {
    if (!viewModel.hasCards) {
      return FadeInUp(
        delay: const Duration(milliseconds: 400),
        duration: const Duration(milliseconds: 800),
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              Icon(
                PhosphorIcons.warningCircle(),
                size: 64,
                color: theme.colorScheme.primary.withValues(alpha: 0.7),
              ),
              const SizedBox(height: 16),
              Text(
                'No Cards to Study',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add some flashcards to this deck to start studying',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FadeInLeft(
          delay: const Duration(milliseconds: 400),
          duration: const Duration(milliseconds: 800),
          child: Text(
            'Study Modes',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Practice Session
        FadeInUp(
          delay: const Duration(milliseconds: 600),
          duration: const Duration(milliseconds: 800),
          child: _buildStudyModeCard(
            title: 'Practice Session',
            description: 'Full interactive session with progress tracking',
            icon: PhosphorIcons.play(),
            gradient: AppTheme.primaryGradient,
            onTap: () => _startStudyMode(viewModel, StudyMode.practice),
            theme: theme,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Quick Review
        FadeInUp(
          delay: const Duration(milliseconds: 800),
          duration: const Duration(milliseconds: 800),
          child: _buildStudyModeCard(
            title: 'Quick Review',
            description: 'Browse through cards at your own pace',
            icon: PhosphorIcons.eye(),
            gradient: AppTheme.secondaryGradient,
            onTap: () => _startStudyMode(viewModel, StudyMode.review),
            theme: theme,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Random Practice
        FadeInUp(
          delay: const Duration(milliseconds: 1000),
          duration: const Duration(milliseconds: 800),
          child: _buildStudyModeCard(
            title: 'Shuffle Practice',
            description: 'Randomized order for enhanced learning',
            icon: PhosphorIcons.shuffle(),
            gradient: AppTheme.successGradient,
            onTap: () => _startStudyMode(viewModel, StudyMode.test),
            theme: theme,
          ),
        ),
      ],
    );
  }

  Widget _buildStudyModeCard({
    required String title,
    required String description,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient.map((c) => c.withValues(alpha: 0.1)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: gradient.first.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: gradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              PhosphorIcons.arrowRight(),
              color: gradient.first,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(ThemeData theme, StudyViewModel viewModel) {
    return FadeInUp(
      delay: const Duration(milliseconds: 1200),
      duration: const Duration(milliseconds: 800),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Stats',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'Total Cards',
                  value: viewModel.totalCards.toString(),
                  icon: PhosphorIcons.cards(),
                  color: theme.colorScheme.primary,
                  theme: theme,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'Last Studied',
                  value: viewModel.lastStudiedText,
                  icon: PhosphorIcons.clock(),
                  color: theme.colorScheme.secondary,
                  theme: theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _startStudyMode(StudyViewModel viewModel, StudyMode mode) {
    viewModel.navigateToStudySession(widget.deck, mode);
  }
} 