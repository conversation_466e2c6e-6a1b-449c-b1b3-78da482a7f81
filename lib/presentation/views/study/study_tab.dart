import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import '../../../domain/entities/deck.dart';
import '../../../domain/entities/study_mode.dart';
import '../../viewmodels/deck_view_model.dart';
import '../../viewmodels/study_session_view_model.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/di/injection_container.dart';
import 'study_session_view.dart';

class StudyTab extends StatefulWidget {
  const StudyTab({super.key});

  @override
  State<StudyTab> createState() => _StudyTabState();
}

class _StudyTabState extends State<StudyTab> {
  StudyMode? _selectedMode;

  void _selectStudyMode(StudyMode mode) {
    setState(() {
      _selectedMode = mode;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: Consumer<DeckViewModel>(
          builder: (context, deckViewModel, child) {
            if (deckViewModel.decks.isEmpty) {
              return _buildEmptyState(context);
            }

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                const SliverToBoxAdapter(child: SizedBox(height: 60)),

                // Study Mode Selection
                SliverToBoxAdapter(
                  child: _buildStudyModeSelection(context),
                ),

                // Available Decks Header
                SliverToBoxAdapter(
                  child: _buildDecksHeader(theme, deckViewModel),
                ),

                // Deck List
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(24, 0, 24, 120),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final deck = deckViewModel.decks[index];
                        return _buildMinimalDeckCard(context, deck, index);
                      },
                      childCount: deckViewModel.decks.length,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStudyModeSelection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.08),
            theme.colorScheme.surface.withValues(alpha: 0.04),
          ],
        ),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.05),
          width: 0.5,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(32),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Choose Study Mode',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w300,
                ),
              ),
              const SizedBox(height: 16),
              _buildModeGrid(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModeGrid(ThemeData theme) {
    final modes = [
      _StudyModeInfo(
        mode: StudyMode.review,
        title: 'Review',
        description: 'Quick review of all cards',
        icon: Icons.refresh_rounded,
        gradient: AppTheme.primaryGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.learn,
        title: 'Learn',
        description: 'Focus on new cards',
        icon: Icons.school_rounded,
        gradient: AppTheme.secondaryGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.test,
        title: 'Test',
        description: 'Challenge yourself',
        icon: Icons.quiz_rounded,
        gradient: AppTheme.accentGradient,
      ),
      _StudyModeInfo(
        mode: StudyMode.weakCards,
        title: 'Weak Cards',
        description: 'Practice difficult cards',
        icon: Icons.trending_up_rounded,
        gradient: [const Color(0xFFEC4899), const Color(0xFFDC2626)],
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: modes.length,
      itemBuilder: (context, index) {
        final mode = modes[index];
        final isSelected = _selectedMode == mode.mode;
        
        return GestureDetector(
          onTap: () => _selectStudyMode(mode.mode),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                colors: isSelected
                    ? mode.gradient.map((c) => c.withValues(alpha: 0.15)).toList()
                    : mode.gradient.map((c) => c.withValues(alpha: 0.05)).toList(),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              border: Border.all(
                color: isSelected
                    ? mode.gradient[0].withValues(alpha: 0.3)
                    : mode.gradient[0].withValues(alpha: 0.1),
                width: isSelected ? 1.0 : 0.5,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: mode.gradient[0].withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ]
                  : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        mode.icon,
                        color: mode.gradient[0],
                        size: 18,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        mode.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Expanded(
                        child: Text(
                          mode.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w300,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDecksHeader(ThemeData theme, DeckViewModel deckViewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Available Decks',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w300,
            ),
          ),
          Text(
            '${deckViewModel.decks.length} decks',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalDeckCard(BuildContext context, Deck deck, int index) {
    final theme = Theme.of(context);
    final gradients = [
      AppTheme.primaryGradient,
      AppTheme.secondaryGradient,
      AppTheme.accentGradient,
      [const Color(0xFFEC4899), const Color(0xFFDC2626)],
    ];
    final gradient = gradients[index % gradients.length];

    return GestureDetector(
      onTap: () => _startStudySession(context, deck),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32),
          gradient: LinearGradient(
            colors: gradient.map((c) => c.withValues(alpha: 0.06)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(
            color: gradient[0].withValues(alpha: 0.12),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: gradient[0].withValues(alpha: 0.06),
              blurRadius: 24,
              offset: const Offset(0, 12),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(32),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: gradient.map((c) => c.withValues(alpha: 0.15)).toList(),
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Icon(
                      Icons.layers_outlined,
                      color: gradient[0],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          deck.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              '${deck.flashcards.length} cards',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                            if (deck.lastStudied != null) ...[
                              const SizedBox(width: 8),
                              Container(
                                width: 4,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _getLastStudiedText(deck.lastStudied!),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (_selectedMode != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          colors: gradient,
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.play_arrow_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Start',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      size: 16,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getLastStudiedText(DateTime lastStudied) {
    final now = DateTime.now();
    final difference = now.difference(lastStudied);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _startStudySession(BuildContext context, Deck deck) {
    if (_selectedMode == null) {
      // Show mode selection dialog
      _showStudyModeDialog(context, deck);
    } else {
      // Navigate to study session
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => getIt<StudySessionViewModel>(),
            child: StudySessionView(
              deck: deck,
              studyMode: _selectedMode!,
            ),
          ),
        ),
      );
    }
  }

  void _showStudyModeDialog(BuildContext context, Deck deck) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Choose Study Mode'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: StudyMode.values.map((mode) {
            return ListTile(
              leading: Icon(_getStudyModeIcon(mode)),
              title: Text(mode.displayName),
              subtitle: Text(mode.description),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ChangeNotifierProvider(
                      create: (_) => getIt<StudySessionViewModel>(),
                      child: StudySessionView(
                        deck: deck,
                        studyMode: mode,
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  IconData _getStudyModeIcon(StudyMode mode) {
    switch (mode) {
      case StudyMode.review:
        return Icons.refresh_rounded;
      case StudyMode.practice:
        return Icons.fitness_center_rounded;
      case StudyMode.test:
        return Icons.quiz_rounded;
      case StudyMode.learn:
        return Icons.school_rounded;
      case StudyMode.weakCards:
        return Icons.trending_up_rounded;
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(48),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 24),
            Text(
              'No decks to study',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Create your first deck to start studying',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _StudyModeInfo {
  final StudyMode mode;
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradient;

  _StudyModeInfo({
    required this.mode,
    required this.title,
    required this.description,
    required this.icon,
    required this.gradient,
  });
} 