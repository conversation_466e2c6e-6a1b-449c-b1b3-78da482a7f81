import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import 'package:confetti/confetti.dart';
import '../../../domain/entities/deck.dart';
import '../../../domain/entities/study_mode.dart';
import '../../viewmodels/study_session_view_model.dart';
import '../../widgets/animated_flashcard_widget.dart';

class StudySessionView extends StatefulWidget {
  final Deck deck;
  final StudyMode studyMode;

  const StudySessionView({
    super.key,
    required this.deck,
    required this.studyMode,
  });

  @override
  State<StudySessionView> createState() => _StudySessionViewState();
}

class _StudySessionViewState extends State<StudySessionView>
    with TickerProviderStateMixin {
  
  late AnimationController _progressController;
  late AnimationController _cardController;
  late AnimationController _statsController;
  late ConfettiController _confettiController;
  
  late Animation<double> _progressAnimation;
  late Animation<double> _cardSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  late Animation<double> _statsAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSession();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _statsController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    _cardSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOutCubic,
    ));

    _cardFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOut,
    ));

    _statsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _statsController,
      curve: Curves.elasticOut,
    ));

    _progressController.forward();
    _cardController.forward();
    _statsController.forward();
  }

  void _startSession() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<StudySessionViewModel>();
      viewModel.startSession(widget.deck, widget.studyMode);
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _cardController.dispose();
    _statsController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<StudySessionViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoading) {
          return _buildLoadingScreen(theme);
        }

        if (viewModel.hasError) {
          return _buildErrorScreen(theme, viewModel.error!);
        }

        if (viewModel.isSessionComplete) {
          return _buildCompletionScreen(theme, viewModel);
        }

        return _buildStudyScreen(theme, viewModel);
      },
    );
  }

  Widget _buildLoadingScreen(ThemeData theme) {
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'Preparing your study session...',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(ThemeData theme, String error) {
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(PhosphorIcons.arrowLeft()),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                PhosphorIcons.warning(),
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Study Session Error',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                error,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 32),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStudyScreen(ThemeData theme, StudySessionViewModel viewModel) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(theme, viewModel),
      body: Container(
        decoration: _buildBackgroundDecoration(theme),
        child: SafeArea(
          child: Column(
            children: [
              _buildProgressSection(theme, viewModel),
              Expanded(
                child: _buildCardSection(theme, viewModel),
              ),
              _buildControlsSection(theme, viewModel),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, StudySessionViewModel viewModel) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          PhosphorIcons.x(),
          color: theme.colorScheme.onSurface,
        ),
        onPressed: () => _showExitDialog(context, viewModel),
      ),
      title: FadeIn(
        duration: const Duration(milliseconds: 600),
        child: Column(
          children: [
            Text(
              widget.studyMode.displayName,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              widget.deck.name,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            PhosphorIcons.clockCounterClockwise(),
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => _showResetDialog(context, viewModel),
        ),
      ],
    );
  }

  BoxDecoration _buildBackgroundDecoration(ThemeData theme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: theme.brightness == Brightness.dark
            ? [
                const Color(0xFF0F172A),
                const Color(0xFF1E293B),
                const Color(0xFF334155),
              ]
            : [
                const Color(0xFFFAFAFA),
                const Color(0xFFF1F5F9),
                const Color(0xFFE2E8F0),
              ],
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme, StudySessionViewModel viewModel) {
    return AnimatedBuilder(
      animation: _progressAnimation,
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatCard(
                  theme,
                  'Progress',
                  viewModel.progressText,
                  PhosphorIcons.chartLine(),
                ),
                _buildStatCard(
                  theme,
                  'Accuracy',
                  viewModel.accuracyText,
                  PhosphorIcons.target(),
                ),
                _buildStatCard(
                  theme,
                  'Time',
                  viewModel.sessionDurationText,
                  PhosphorIcons.timer(),
                ),
              ],
            ),
            const SizedBox(height: 24),
            LinearProgressIndicator(
              value: viewModel.progress,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      ),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - _progressAnimation.value)),
          child: Opacity(
            opacity: _progressAnimation.value,
            child: child,
          ),
        );
      },
    );
  }

  Widget _buildStatCard(ThemeData theme, String label, String value, IconData icon) {
    return AnimatedBuilder(
      animation: _statsAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _statsAnimation.value,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardSection(ThemeData theme, StudySessionViewModel viewModel) {
    if (viewModel.currentCard == null) {
      return const Center(
        child: Text('No card available'),
      );
    }

    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _cardSlideAnimation.value),
          child: Opacity(
            opacity: _cardFadeAnimation.value,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: AnimatedFlashcardWidget(
                flashcard: viewModel.currentCard!,
                showAnswer: viewModel.showAnswer,
                isFlipping: viewModel.isFlipping,
                onTap: () => viewModel.toggleAnswer(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlsSection(ThemeData theme, StudySessionViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          if (!viewModel.showAnswer) ...[
            FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: FilledButton.icon(
                onPressed: viewModel.isFlipping ? null : () => viewModel.toggleAnswer(),
                icon: Icon(PhosphorIcons.eye()),
                label: const Text('Show Answer'),
                style: FilledButton.styleFrom(
                  minimumSize: const Size(double.infinity, 56),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
          ] else ...[
            FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Row(
                children: [
                  Expanded(
                    child: FilledButton.icon(
                      onPressed: viewModel.isTransitioning 
                          ? null 
                          : () => _answerCard(viewModel, false),
                      icon: Icon(PhosphorIcons.x()),
                      label: const Text('Incorrect'),
                      style: FilledButton.styleFrom(
                        backgroundColor: theme.colorScheme.error,
                        foregroundColor: theme.colorScheme.onError,
                        minimumSize: const Size(0, 56),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: FilledButton.icon(
                      onPressed: viewModel.isTransitioning 
                          ? null 
                          : () => _answerCard(viewModel, true),
                      icon: Icon(PhosphorIcons.check()),
                      label: const Text('Correct'),
                      style: FilledButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(0, 56),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: viewModel.hasPreviousCard ? () => viewModel.previousCard() : null,
                icon: Icon(PhosphorIcons.caretLeft()),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  foregroundColor: theme.colorScheme.onSurface,
                  minimumSize: const Size(48, 48),
                ),
              ),
              Text(
                '${viewModel.currentCardIndex + 1} of ${viewModel.totalCards}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              IconButton(
                onPressed: viewModel.hasNextCard ? () => viewModel.nextCard() : null,
                icon: Icon(PhosphorIcons.caretRight()),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  foregroundColor: theme.colorScheme.onSurface,
                  minimumSize: const Size(48, 48),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionScreen(ThemeData theme, StudySessionViewModel viewModel) {
    // Trigger confetti when session completes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _confettiController.play();
    });

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Stack(
        children: [
          Container(
            decoration: _buildBackgroundDecoration(theme),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FadeInDown(
                      duration: const Duration(milliseconds: 800),
                      child: Icon(
                        PhosphorIcons.trophy(),
                        size: 80,
                        color: Colors.amber,
                      ),
                    ),
                    const SizedBox(height: 24),
                    FadeInUp(
                      duration: const Duration(milliseconds: 800),
                      delay: const Duration(milliseconds: 200),
                      child: Text(
                        'Session Complete!',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    FadeInUp(
                      duration: const Duration(milliseconds: 800),
                      delay: const Duration(milliseconds: 400),
                      child: Text(
                        'Great job studying ${widget.deck.name}',
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                    const SizedBox(height: 48),
                    _buildResultsCard(theme, viewModel),
                    const SizedBox(height: 48),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => viewModel.resetSession(),
                            icon: Icon(PhosphorIcons.arrowCounterClockwise()),
                            label: const Text('Study Again'),
                            style: OutlinedButton.styleFrom(
                              minimumSize: const Size(0, 56),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: FilledButton.icon(
                            onPressed: () => viewModel.navigateBack(),
                            icon: Icon(PhosphorIcons.house()),
                            label: const Text('Done'),
                            style: FilledButton.styleFrom(
                              minimumSize: const Size(0, 56),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: 1.5708, // radians for downward
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              gravity: 0.1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsCard(ThemeData theme, StudySessionViewModel viewModel) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 600),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildResultStat(
                  theme,
                  'Accuracy',
                  viewModel.accuracyText,
                  PhosphorIcons.target(),
                  Colors.green,
                ),
                _buildResultStat(
                  theme,
                  'Time',
                  viewModel.sessionDurationText,
                  PhosphorIcons.timer(),
                  theme.colorScheme.primary,
                ),
                _buildResultStat(
                  theme,
                  'Cards',
                  '${viewModel.totalCards}',
                  PhosphorIcons.cards(),
                  Colors.orange,
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildScoreCard(
                    theme,
                    'Correct',
                    '${viewModel.correctAnswers}',
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildScoreCard(
                    theme,
                    'Incorrect',
                    '${viewModel.incorrectAnswers}',
                    theme.colorScheme.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultStat(ThemeData theme, String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: color,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildScoreCard(ThemeData theme, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  void _answerCard(StudySessionViewModel viewModel, bool isCorrect) {
    HapticFeedback.lightImpact();
    viewModel.answerCard(isCorrect);
    
    // Animate card transition
    _cardController.reset();
    _cardController.forward();
  }

  void _showExitDialog(BuildContext context, StudySessionViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Study Session?'),
        content: const Text('Your progress will be saved, but the session will end.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              viewModel.navigateBack();
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(BuildContext context, StudySessionViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Session?'),
        content: const Text('This will restart the session with the same cards in a new order.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              viewModel.resetSession();
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
} 