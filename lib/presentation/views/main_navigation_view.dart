import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../core/di/injection_container.dart';
import 'home/home_view.dart';
import 'study/study_tab.dart';
import 'analytics/analytics_tab.dart';
import 'settings/settings_tab.dart';
import '../viewmodels/home_view_model.dart';
import '../viewmodels/deck_view_model.dart';
import '../viewmodels/main_navigation_view_model.dart';
import '../viewmodels/theme_view_model.dart';

class MainNavigationView extends StatefulWidget {
  const MainNavigationView({super.key});

  @override
  State<MainNavigationView> createState() => _MainNavigationViewState();
}

class _MainNavigationViewState extends State<MainNavigationView>
    with TickerProviderStateMixin {
  
  // Simplified animation controllers
  late AnimationController _navAnimationController;

  final List<_TabInfo> _tabs = [
    _TabInfo(
      icon: Icons.home_outlined,
      selectedIcon: Icons.home_rounded,
      label: 'Home',
      page: MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (context) => getIt<HomeViewModel>()..loadDecks(),
          ),
          ChangeNotifierProvider(
            create: (context) => getIt<ThemeViewModel>(),
          ),
        ],
        child: const HomeView(),
      ),
      gradientColors: [
        const Color(0xFF9333EA),
        const Color(0xFF7C3AED),
        const Color(0xFF6366F1),
      ],
    ),
    _TabInfo(
      icon: Icons.school_outlined,
      selectedIcon: Icons.school_rounded,
      label: 'Study',
      page: const StudyTab(), // Keep original for now
      gradientColors: [
        const Color(0xFF06B6D4), 
        const Color(0xFF3B82F6),
        const Color(0xFF1E40AF),
      ],
    ),
    _TabInfo(
      icon: Icons.analytics_outlined,
      selectedIcon: Icons.analytics_rounded,
      label: 'Analytics',
      page: const AnalyticsTab(), // Keep original for now
      gradientColors: [
        const Color(0xFFFFB800), 
        const Color(0xFFFF8A00),
        const Color(0xFFEF4444),
      ],
    ),
    _TabInfo(
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings_rounded,
      label: 'Settings',
      page: const SettingsTab(), // Keep original for now
      gradientColors: [
        const Color(0xFFEC4899), 
        const Color(0xFFDC2626),
        const Color(0xFF7C2D12),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    
    // Simplified animation controller
    _navAnimationController = AnimationController(
      duration: AppTheme.longDuration,
      vsync: this,
    );

    // Start entrance animation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _navAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => getIt<MainNavigationViewModel>(),
      child: Consumer<MainNavigationViewModel>(
        builder: (context, viewModel, child) {
          final theme = Theme.of(context);

          return Scaffold(
            extendBody: true,
            backgroundColor: theme.colorScheme.surface,
            body: AnimatedContainer(
              duration: AppTheme.extraLongDuration,
              curve: AppTheme.smoothCurve,
              decoration: BoxDecoration(
                gradient: theme.brightness == Brightness.dark
                    ? AppTheme.surfaceLinearGradient
                    : LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          theme.colorScheme.surface,
                          theme.colorScheme.surfaceContainerHighest,
                        ],
                      ),
              ),
              child: IndexedStack(
                index: viewModel.currentIndex,
                children: [..._tabs.map((tab) => tab.page)],
              ),
            ),
            bottomNavigationBar: _buildBottomNavBar(theme, viewModel),
          );
        },
      ),
    );
  }

  Widget _buildBottomNavBar(ThemeData theme, MainNavigationViewModel viewModel) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Regular nav items
          ..._tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tab = entry.value;
            final isSelected = index == viewModel.currentIndex;

            return Expanded(
              child: _buildNavItem(
                theme: theme,
                tab: tab,
                isSelected: isSelected,
                isAdjacent: false,
                onTap: () => viewModel.onTabTapped(index),
              ),
            );
          }),
          // Create button
          Expanded(
            child: _buildCreateButton(context, theme, viewModel),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateButton(BuildContext context, ThemeData theme, MainNavigationViewModel viewModel) {
    return GestureDetector(
      onTap: () async {
        final result = await viewModel.navigateToCreateDeck();
        // If a deck was created, reload the decks
        if (result == true && context.mounted) {
          final deckViewModel = context.read<DeckViewModel>();
          deckViewModel.loadDecks();
        }
      },
      child: Container(
        height: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: theme.colorScheme.onSurface.withValues(alpha: 0.1),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 0.5,
                ),
              ),
              child: Icon(
                Icons.add,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                size: 18,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Create',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w400,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required ThemeData theme,
    required _TabInfo tab,
    required bool isSelected,
    required bool isAdjacent,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedSwitcher(
              duration: AppTheme.microDuration,
              child: Icon(
                isSelected ? tab.selectedIcon : tab.icon,
                key: ValueKey(isSelected),
                color: isSelected
                    ? tab.gradientColors[0]
                    : theme.colorScheme.outline.withValues(alpha: 0.6),
                size: 20,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              tab.label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isSelected
                    ? tab.gradientColors[0]
                    : theme.colorScheme.outline.withValues(alpha: 0.6),
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TabInfo {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final Widget page;
  final List<Color> gradientColors;

  _TabInfo({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.page,
    required this.gradientColors,
  });
} 