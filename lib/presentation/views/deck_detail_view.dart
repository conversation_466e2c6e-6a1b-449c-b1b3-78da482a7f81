import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';

import '../viewmodels/deck_detail_view_model.dart';
import '../../core/di/injection_container.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../core/theme/app_theme.dart';

class DeckDetailView extends StatefulWidget {
  final Deck deck;

  const DeckDetailView({super.key, required this.deck});

  @override
  State<DeckDetailView> createState() => _DeckDetailViewState();
}

class _DeckDetailViewState extends State<DeckDetailView>
    with TickerProviderStateMixin {

  // Animation controllers for sophisticated micro interactions
  late AnimationController _masterController;
  late AnimationController _heroController;
  late AnimationController _cardsController;
  late AnimationController _fabController;
  late AnimationController _floatController;
  
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _heroAnimation;
  late Animation<double> _cardsAnimation;
  late Animation<double> _fabAnimation;
  late Animation<double> _floatAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _masterController = AnimationController(
      duration: AppTheme.extraLongDuration,
      vsync: this,
    );
    
    _heroController = AnimationController(
      duration: AppTheme.longDuration,
      vsync: this,
    );
    
    _cardsController = AnimationController(
      duration: AppTheme.longDuration,
      vsync: this,
    );
    
    _fabController = AnimationController(
      duration: AppTheme.mediumDuration,
      vsync: this,
    );

    _floatController = AnimationController(
      duration: const Duration(milliseconds: 3500),
      vsync: this,
    );

    // Sophisticated animation curves
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.0, 0.7, curve: Curves.easeOutQuart),
    ));

    _slideAnimation = Tween<double>(
      begin: 60.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.2, 0.9, curve: Curves.easeOutCubic),
    ));

    _heroAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heroController,
      curve: AppTheme.springCurve,
    ));

    _cardsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardsController,
      curve: AppTheme.smoothCurve,
    ));

    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabController,
      curve: AppTheme.springCurve,
    ));

    _floatAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _floatController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _masterController.forward();
        _floatController.repeat(reverse: true);
        
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) _heroController.forward();
        });
        
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) _cardsController.forward();
        });
        
        Future.delayed(const Duration(milliseconds: 800), () {
          if (mounted) _fabController.forward();
        });
      }
    });
  }

  @override
  void dispose() {
    _masterController.dispose();
    _heroController.dispose();
    _cardsController.dispose();
    _fabController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => getIt<DeckDetailViewModel>()..loadDeck(widget.deck.id),
      child: Consumer<DeckDetailViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            appBar: _buildMinimalAppBar(context, viewModel),
            body: _buildBody(context, viewModel),
            floatingActionButton: _buildMinimalFAB(context, viewModel),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);
    
    if (viewModel.isLoading) {
      return _buildLoadingState(theme);
    }
    
    if (viewModel.hasError) {
      return _buildErrorState(theme, viewModel);
    }
    
    final deck = viewModel.currentDeck ?? widget.deck;
    
    return Container(
      decoration: BoxDecoration(
        gradient: theme.brightness == Brightness.dark
            ? AppTheme.surfaceLinearGradient
            : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerHighest,
                ],
              ),
      ),
      child: AnimatedBuilder(
        animation: _masterController,
        builder: (context, child) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              const SliverToBoxAdapter(child: SizedBox(height: 120)),
              
              // Hero Section
              SliverToBoxAdapter(
                child: Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildHeroSection(theme, deck),
                  ),
                ),
              ),
              
              // Stats Section
              SliverToBoxAdapter(
                child: Transform.translate(
                  offset: Offset(0, _slideAnimation.value * 0.7),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildStatsSection(theme, viewModel),
                  ),
                ),
              ),
              
              // Flashcards Section
              if (viewModel.hasCards)
                SliverToBoxAdapter(
                  child: Transform.translate(
                    offset: Offset(0, _slideAnimation.value * 0.5),
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: _buildFlashcardsHeader(theme, viewModel),
                    ),
                  ),
                ),
              
              // Flashcards List
              if (viewModel.hasCards)
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(24, 0, 24, 120),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final flashcard = viewModel.flashcards[index];
                        return Transform.translate(
                          offset: Offset(0, _slideAnimation.value * 0.3),
                          child: Opacity(
                            opacity: _fadeAnimation.value,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: _FlashcardCard(
                                flashcard: flashcard,
                                index: index,
                                onEdit: () => viewModel.handleEditFlashcard(flashcard),
                                onDelete: () => viewModel.handleDeleteFlashcard(flashcard),
                              ),
                            ),
                          ),
                        );
                      },
                      childCount: viewModel.flashcards.length,
                    ),
                  ),
                )
              else
                SliverToBoxAdapter(
                  child: Transform.translate(
                    offset: Offset(0, _slideAnimation.value * 0.3),
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: _buildEmptyState(context, viewModel),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading deck...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, DeckDetailViewModel viewModel) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              viewModel.error ?? 'An unknown error occurred',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => viewModel.loadDeck(widget.deck.id),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildMinimalAppBar(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);
    final deck = viewModel.currentDeck ?? widget.deck;
    
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: AnimatedBuilder(
        animation: _masterController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                  width: 0.5,
                ),
              ),
              child: ClipOval(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: viewModel.navigateBack,
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: theme.colorScheme.onSurface,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
      title: AnimatedBuilder(
        animation: _masterController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fadeAnimation.value,
            child: Text(
              deck.name,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w300,
                color: theme.colorScheme.onSurface,
              ),
            ),
          );
        },
      ),
      actions: [
        if (viewModel.canStartStudy)
          Container(
            margin: const EdgeInsets.only(right: 16),
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
                width: 0.5,
              ),
            ),
            child: ClipOval(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: viewModel.navigateToStudy,
                    child: Icon(
                      Icons.school_outlined,
                      color: theme.colorScheme.onSurface,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHeroSection(ThemeData theme, Deck deck) {
    return AnimatedBuilder(
      animation: Listenable.merge([_heroAnimation, _floatAnimation]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatAnimation.value),
          child: Transform.scale(
            scale: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
                    theme.colorScheme.surface.withValues(alpha: 0.05),
                  ],
                ),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.05),
                  width: 0.5,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(32),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 64,
                        height: 64,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
                          border: Border.all(
                            color: theme.colorScheme.outline.withValues(alpha: 0.1),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.layers_outlined,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          size: 32,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        deck.name,
                        style: theme.textTheme.headlineLarge?.copyWith(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w200,
                        ),
                      ),
                      if (deck.description.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Text(
                          deck.description,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsSection(ThemeData theme, DeckDetailViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              theme: theme,
              title: 'Cards',
              value: '${viewModel.totalCards}',
              gradient: AppTheme.primaryGradient,
              icon: Icons.layers_outlined,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              theme: theme,
              title: 'Reviews',
              value: '${viewModel.totalReviews}',
              gradient: AppTheme.secondaryGradient,
              icon: Icons.repeat_outlined,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required ThemeData theme,
    required String title,
    required String value,
    required List<Color> gradient,
    required IconData icon,
  }) {
    return AnimatedBuilder(
      animation: Listenable.merge([_cardsAnimation, _fadeAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _fadeAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                colors: gradient.map((c) => c.withValues(alpha: 0.1)).toList(),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.05),
                width: 0.5,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  icon,
                  color: gradient.first,
                  size: 24,
                ),
                const SizedBox(height: 12),
                Text(
                  value,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w300,
                  ),
                ),
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFlashcardsHeader(ThemeData theme, DeckDetailViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Flashcards (${viewModel.totalCards})',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w300,
            ),
          ),
          TextButton.icon(
            onPressed: viewModel.navigateToAddFlashcard,
            icon: const Icon(Icons.add, size: 20),
            label: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(48),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.layers_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 24),
          Text(
            'No flashcards yet',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Add your first flashcard to start studying',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: viewModel.navigateToAddFlashcard,
            icon: const Icon(Icons.add),
            label: const Text('Add Flashcard'),
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalFAB(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _fabAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimation.value,
          child: FloatingActionButton(
            onPressed: viewModel.navigateToAddFlashcard,
            backgroundColor: theme.colorScheme.primary,
            child: Icon(
              Icons.add,
              color: theme.colorScheme.onPrimary,
            ),
          ),
        );
      },
    );
  }
}

// Flashcard Card Widget (Pure UI Component)
class _FlashcardCard extends StatelessWidget {
  final Flashcard flashcard;
  final int index;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _FlashcardCard({
    required this.flashcard,
    required this.index,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Card ${index + 1}',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: onEdit,
                      icon: const Icon(Icons.edit_outlined, size: 20),
                    ),
                    IconButton(
                      onPressed: onDelete,
                      icon: const Icon(Icons.delete_outline, size: 20),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Front: ${flashcard.front}',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Back: ${flashcard.back}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            if (flashcard.reviewCount > 0) ...[
              const SizedBox(height: 8),
              Text(
                'Reviews: ${flashcard.reviewCount}',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 