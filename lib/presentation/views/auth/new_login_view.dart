import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../widgets/auth/auth_text_field_widget.dart';
import '../../widgets/auth/auth_button_widget.dart';
import '../../viewmodels/auth/login_view_model.dart';

class NewLoginView extends StatefulWidget {
  const NewLoginView({super.key});

  @override
  State<NewLoginView> createState() => _NewLoginViewState();
}

class _NewLoginViewState extends State<NewLoginView>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.primary.withValues(alpha: 0.03),
              colorScheme.surface,
              colorScheme.primaryContainer.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer<LoginViewModel>(
            builder: (context, viewModel, child) {
              return SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        const SizedBox(height: 40),
                        
                        // Back Button and Header
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Row(
                              children: [
                                IconButton(
                                  onPressed: () => viewModel.navigateBack(),
                                  icon: Icon(
                                    Icons.arrow_back_ios_rounded,
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                const Spacer(),
                              ],
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Logo
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: SlideTransition(
                                  position: _slideAnimation,
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: colorScheme.primary,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: colorScheme.primary.withValues(alpha: 0.2),
                                          blurRadius: 16,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.auto_stories_rounded,
                                      size: 40,
                                      color: colorScheme.onPrimary,
                                    ),
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              // Welcome Text
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: SlideTransition(
                                  position: _slideAnimation,
                                  child: Column(
                                    children: [
                                      Text(
                                        'Welcome Back',
                                        style: theme.textTheme.headlineMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Sign in to continue your learning journey',
                                        style: theme.textTheme.bodyLarge?.copyWith(
                                          color: colorScheme.onSurfaceVariant,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              
                              const SizedBox(height: 48),
                              
                              // Login Form
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: SlideTransition(
                                  position: _slideAnimation,
                                  child: Form(
                                    key: _formKey,
                                    child: Column(
                                      children: [
                                        // Email Field
                                        AuthTextFieldWidget(
                                          controller: _emailController,
                                          focusNode: _emailFocusNode,
                                          label: 'Email',
                                          hint: 'Enter your email address',
                                          prefixIcon: Icons.email_rounded,
                                          keyboardType: TextInputType.emailAddress,
                                          validator: (value) {
                                            if (value?.isEmpty ?? true) {
                                              return 'Please enter your email';
                                            }
                                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                                .hasMatch(value!)) {
                                              return 'Please enter a valid email';
                                            }
                                            return null;
                                          },
                                          onChanged: (value) {
                                            if (viewModel.error != null) {
                                              viewModel.clearError();
                                            }
                                          },
                                        ),
                                        
                                        const SizedBox(height: 24),
                                        
                                        // Password Field
                                        AuthTextFieldWidget(
                                          controller: _passwordController,
                                          focusNode: _passwordFocusNode,
                                          label: 'Password',
                                          hint: 'Enter your password',
                                          prefixIcon: Icons.lock_rounded,
                                          isPassword: true,
                                          validator: (value) {
                                            if (value?.isEmpty ?? true) {
                                              return 'Please enter your password';
                                            }
                                            return null;
                                          },
                                          onChanged: (value) {
                                            if (viewModel.error != null) {
                                              viewModel.clearError();
                                            }
                                          },
                                        ),
                                        
                                        const SizedBox(height: 16),
                                        
                                        // Forgot Password
                                        Align(
                                          alignment: Alignment.centerRight,
                                          child: TextButton(
                                            onPressed: () => viewModel.navigateToForgotPassword(),
                                            child: Text(
                                              'Forgot Password?',
                                              style: TextStyle(
                                                color: colorScheme.primary,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ),
                                        
                                        const SizedBox(height: 32),
                                        
                                        // Error Message
                                        if (viewModel.error != null)
                                          Container(
                                            width: double.infinity,
                                            padding: const EdgeInsets.all(16),
                                            margin: const EdgeInsets.only(bottom: 24),
                                            decoration: BoxDecoration(
                                              color: colorScheme.errorContainer.withValues(alpha: 0.1),
                                              borderRadius: BorderRadius.circular(12),
                                              border: Border.all(
                                                color: colorScheme.error.withValues(alpha: 0.2),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.error_outline_rounded,
                                                  color: colorScheme.error,
                                                  size: 20,
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Text(
                                                    viewModel.error!,
                                                    style: TextStyle(
                                                      color: colorScheme.error,
                                                      fontSize: 14,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        
                                        // Sign In Button
                                        AuthButtonWidget(
                                          text: 'Sign In',
                                          icon: Icons.arrow_forward_rounded,
                                          isLoading: viewModel.isLoading,
                                          onPressed: () => _handleLogin(viewModel),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Sign Up Link
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 32),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Don't have an account? ",
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () => viewModel.navigateToSignup(),
                                    child: Text(
                                      'Sign Up',
                                      style: TextStyle(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin(LoginViewModel viewModel) async {
    if (!_formKey.currentState!.validate()) return;

    // Dismiss keyboard
    FocusScope.of(context).unfocus();

    await viewModel.login(
      _emailController.text.trim(),
      _passwordController.text,
    );
  }
} 