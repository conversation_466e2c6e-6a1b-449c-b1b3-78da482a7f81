import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../widgets/auth/auth_text_field_widget.dart';
import '../../widgets/auth/auth_button_widget.dart';
import '../../viewmodels/auth/signup_view_model.dart';

class SignupView extends StatefulWidget {
  const SignupView({super.key});

  @override
  State<SignupView> createState() => _SignupViewState();
}

class _SignupViewState extends State<SignupView>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  final _nameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  final _verificationCodeFocusNode = FocusNode();

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verificationCodeController.dispose();
    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _verificationCodeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.primary.withValues(alpha: 0.03),
              colorScheme.surface,
              colorScheme.primaryContainer.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer<SignupViewModel>(
            builder: (context, viewModel, child) {
              return SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        const SizedBox(height: 40),
                        
                        // Header with Back Button and Progress
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Row(
                              children: [
                                IconButton(
                                  onPressed: () => _handleBackPress(viewModel),
                                  icon: Icon(
                                    Icons.arrow_back_ios_rounded,
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                                const Spacer(),
                                if (viewModel.currentStep != SignupStep.basicInfo)
                                  _buildStepIndicator(viewModel),
                              ],
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        Expanded(
                          child: _buildCurrentStep(viewModel, theme, colorScheme),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentStep(SignupViewModel viewModel, ThemeData theme, ColorScheme colorScheme) {
    switch (viewModel.currentStep) {
      case SignupStep.basicInfo:
        return _buildBasicInfoStep(viewModel, theme, colorScheme);
      case SignupStep.verification:
        return _buildVerificationStep(viewModel, theme, colorScheme);
      case SignupStep.completed:
        return _buildCompletedStep(viewModel, theme, colorScheme);
    }
  }

  Widget _buildBasicInfoStep(SignupViewModel viewModel, ThemeData theme, ColorScheme colorScheme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Logo
        FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                    blurRadius: 16,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Icon(
                Icons.auto_stories_rounded,
                size: 40,
                color: colorScheme.onPrimary,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Welcome Text
        FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                Text(
                  'Create Account',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Join thousands of learners worldwide',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 48),
        
        // Signup Form
        FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Name Field
                  AuthTextFieldWidget(
                    controller: _nameController,
                    focusNode: _nameFocusNode,
                    label: 'Full Name',
                    hint: 'Enter your full name',
                    prefixIcon: Icons.person_rounded,
                    textCapitalization: TextCapitalization.words,
                    validator: viewModel.validateName,
                    onChanged: (value) {
                      if (viewModel.error != null) {
                        viewModel.clearError();
                      }
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Email Field
                  AuthTextFieldWidget(
                    controller: _emailController,
                    focusNode: _emailFocusNode,
                    label: 'Email',
                    hint: 'Enter your email address',
                    prefixIcon: Icons.email_rounded,
                    keyboardType: TextInputType.emailAddress,
                    validator: viewModel.validateEmail,
                    onChanged: (value) {
                      if (viewModel.error != null) {
                        viewModel.clearError();
                      }
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Password Field
                  AuthTextFieldWidget(
                    controller: _passwordController,
                    focusNode: _passwordFocusNode,
                    label: 'Password',
                    hint: 'Create a strong password',
                    prefixIcon: Icons.lock_rounded,
                    isPassword: true,
                    validator: viewModel.validatePassword,
                    onChanged: (value) {
                      if (viewModel.error != null) {
                        viewModel.clearError();
                      }
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Confirm Password Field
                  AuthTextFieldWidget(
                    controller: _confirmPasswordController,
                    focusNode: _confirmPasswordFocusNode,
                    label: 'Confirm Password',
                    hint: 'Confirm your password',
                    prefixIcon: Icons.lock_outline_rounded,
                    isPassword: true,
                    validator: (value) => viewModel.validateConfirmPassword(
                      value,
                      _passwordController.text,
                    ),
                    onChanged: (value) {
                      if (viewModel.error != null) {
                        viewModel.clearError();
                      }
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Password Requirements
                  _buildPasswordRequirements(theme, colorScheme),
                  
                  const SizedBox(height: 32),
                  
                  // Error Message
                  if (viewModel.error != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 24),
                      decoration: BoxDecoration(
                        color: colorScheme.errorContainer.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.error.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline_rounded,
                            color: colorScheme.error,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              viewModel.error!,
                              style: TextStyle(
                                color: colorScheme.error,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // Create Account Button
                  AuthButtonWidget(
                    text: 'Create Account',
                    icon: Icons.arrow_forward_rounded,
                    isLoading: viewModel.isLoading,
                    onPressed: () => _handleSignup(viewModel),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Login Link
        FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Already have an account? ',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                TextButton(
                  onPressed: () => viewModel.navigateToLogin(),
                  child: Text(
                    'Sign In',
                    style: TextStyle(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationStep(SignupViewModel viewModel, ThemeData theme, ColorScheme colorScheme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Email Icon
        FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              Icons.mark_email_read_rounded,
              size: 50,
              color: colorScheme.primary,
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Verification Text
        FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              Text(
                'Check Your Email',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'We sent a verification code to\n${viewModel.email}',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 48),
        
        // Verification Code Field
        FadeTransition(
          opacity: _fadeAnimation,
          child: AuthTextFieldWidget(
            controller: _verificationCodeController,
            focusNode: _verificationCodeFocusNode,
            label: 'Verification Code',
            hint: 'Enter 6-digit code',
            prefixIcon: Icons.security_rounded,
            keyboardType: TextInputType.number,
            maxLength: 6,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter the verification code';
              }
              if (value!.length != 6) {
                return 'Code must be 6 digits';
              }
              return null;
            },
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Error Message
        if (viewModel.error != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: colorScheme.errorContainer.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colorScheme.error.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline_rounded,
                  color: colorScheme.error,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    viewModel.error!,
                    style: TextStyle(
                      color: colorScheme.error,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        
        // Verify Button
        AuthButtonWidget(
          text: 'Verify Email',
          icon: Icons.verified_rounded,
          isLoading: viewModel.isLoading,
          onPressed: () => _handleVerification(viewModel),
        ),
        
        const SizedBox(height: 24),
        
        // Resend Code
        TextButton(
          onPressed: viewModel.isLoading ? null : () => viewModel.resendVerificationEmail(),
          child: Text(
            'Resend Code',
            style: TextStyle(
              color: colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletedStep(SignupViewModel viewModel, ThemeData theme, ColorScheme colorScheme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Success Icon
        FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Icon(
              Icons.check_circle_rounded,
              size: 50,
              color: Colors.green,
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Success Text
        FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              Text(
                'Welcome Aboard!',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Your account has been created successfully.\nLet\'s start your learning journey!',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 48),
        
        // Continue Button
        AuthButtonWidget(
          text: 'Start Learning',
          icon: Icons.rocket_launch_rounded,
          onPressed: () => viewModel.navigateToLogin(),
        ),
      ],
    );
  }

  Widget _buildPasswordRequirements(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Password Requirements:',
            style: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItem('At least 8 characters', theme, colorScheme),
          _buildRequirementItem('Contains letters', theme, colorScheme),
          _buildRequirementItem('Contains numbers', theme, colorScheme),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text, ThemeData theme, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline_rounded,
            size: 16,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(SignupViewModel viewModel) {
    return Row(
      children: [
        Text(
          'Step ${viewModel.currentStep.index + 1} of 3',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _handleBackPress(SignupViewModel viewModel) {
    if (viewModel.currentStep == SignupStep.basicInfo) {
      viewModel.navigateBack();
    } else {
      viewModel.goToPreviousStep();
    }
  }

  Future<void> _handleSignup(SignupViewModel viewModel) async {
    if (!_formKey.currentState!.validate()) return;

    // Dismiss keyboard
    FocusScope.of(context).unfocus();

    await viewModel.register(
      _nameController.text.trim(),
      _emailController.text.trim(),
      _passwordController.text,
    );
  }

  Future<void> _handleVerification(SignupViewModel viewModel) async {
    if (_verificationCodeController.text.length != 6) {
      viewModel.setError('Please enter a valid 6-digit code');
      return;
    }

    // Dismiss keyboard
    FocusScope.of(context).unfocus();

    await viewModel.verifyEmail(_verificationCodeController.text);
  }
} 