name: flash_cards_app
description: "A Flutter flashcard application with separation of concerns architecture."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # State management
  provider: ^6.1.2
  # Local storage
  shared_preferences: ^2.3.4
  # Utilities
  uuid: ^4.5.1
  equatable: ^2.0.7
  # JSON serialization
  json_annotation: ^4.9.0

  # UI & Icons
  font_awesome_flutter: ^10.7.0
  phosphor_flutter: ^2.1.0
  
  # Animations & UI Enhancements
  animate_do: ^3.3.4
  shimmer: ^3.0.0
  lottie: ^3.1.2
  rive: ^0.13.13
  
  # Charts & Progress indicators
  fl_chart: ^0.69.0
  percent_indicator: ^4.2.3
  
  # UI Components
  glass_kit: ^3.0.0
  blur: ^4.0.0
  confetti: ^0.7.0
  
  # Enhanced Material Design
  dynamic_color: ^1.7.0
  
  # Smooth page transitions
  page_transition: ^2.1.0
  
  # Enhanced animations
  flutter_staggered_animations: ^1.1.1
  
  # Card flip animations
  flip_card: ^0.7.0
  
  # Haptic feedback
  vibration: ^2.0.0
  
  # Custom fonts support
  google_fonts: ^6.2.1

  # MVVM Architecture Dependencies
  # Dependency Injection
  get_it: ^7.7.0
  injectable: ^2.4.4
  
  # Functional Programming
  dartz: ^0.10.1
  
  # Local Storage for MVVM
  hive: ^2.2.3
  hive_flutter: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  # Code generation
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  
  # MVVM Code Generation
  injectable_generator: ^2.6.2
  hive_generator: ^2.0.1
  
  # Testing for MVVM
  mockito: ^5.4.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  # Add custom assets for beautiful UI
  assets:
    - assets/images/
    - assets/lottie/
    - assets/rive/
